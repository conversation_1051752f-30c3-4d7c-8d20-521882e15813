import {
  Component,
  DestroyRef,
  effect,
  inject,
  OnInit,
  signal,
  TemplateRef,
  ViewChild,
} from '@angular/core';
import {
  TranslocoDirective,
  TranslocoPipe,
  TranslocoService,
} from '@jsverse/transloco';
import { DecimalPipe, NgClass, NgTemplateOutlet } from '@angular/common';
import { GoogleAdAccount } from '@api/google/models/google-ad-account.interface';
import { PaginatedResponse } from '@api/support/responses/paginated.response';
import { GoogleAdAccountService } from '@api/google/services/google-ad-account.service';
import { GoogleScriptType } from '@api/google/enums/google-script-type.enum';
import { catchError, filter, map, Observable, tap } from 'rxjs';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import {
  FormControl,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { SelectOption } from '@interfaces/select-option.interface';
import { ConnectedSelectComponent } from '@components/form-inputs/connected-select/connected-select.component';
import { GoogleDriveService } from '@api/google/services/google-drive.service';
import { GoogleDrive } from '@api/google/models/google-drive.interface';
import { GoogleScript } from '@api/google/models/google-script.interface';
import { GoogleScriptStatus } from '@api/google/enums/google-script-status.enum';
import { GoogleScriptService } from '@api/google/services/google-script.service';
import {
  genericToastError,
  genericToastSuccess,
} from '@helpers/generic-toasts';
import { HotToastService } from '@ngxpert/hot-toast';
import { Clipboard } from '@angular/cdk/clipboard';

enum Step {
  SELECT_ACCOUNT = 'SELECT_ACCOUNT',
  SELECT_DRIVE = 'SELECT_DRIVE',
  CREATING_SHEET = 'CREATING_SHEET',
  ADD_SCRIPT = 'ADD_SCRIPT',
}

interface Form {
  google_ad_account_id: FormControl<SelectOption<number> | null>;
  google_drive_id: FormControl<SelectOption<number> | null>;
  google_drive_name: FormControl<string | null>;
}

@Component({
  selector: 'settings-add-ons-quality-score',
  imports: [
    TranslocoDirective,
    NgClass,
    NgTemplateOutlet,
    ConnectedSelectComponent,
    ReactiveFormsModule,
    TranslocoPipe,
    DecimalPipe,
  ],
  templateUrl: './quality-score.component.html',
})
export class QualityScoreComponent implements OnInit {
  public response = signal<PaginatedResponse<GoogleAdAccount> | null>(null);
  public open = signal<boolean>(false);
  public loading = signal<boolean>(false);
  public guiding = signal<boolean>(false);
  public activeStep = signal<Step>(Step.SELECT_ACCOUNT);
  public form = signal<FormGroup<Form> | null>(null);
  public percentageDone = signal<number>(0);
  public script = signal<GoogleScript | null>(null);
  public submitting = signal<boolean>(false);
  private firstSubmit = signal<boolean>(true);
  public renderedScript = signal<string | null>(null);
  public renderLoading = signal<boolean>(false);

  public readonly step = Step;
  public readonly steps = Object.values(Step);
  public readonly statusToPercentage: GoogleScriptStatus[] = [
    GoogleScriptStatus.CREATING_DRIVE,
    GoogleScriptStatus.CREATING_FILE,
    GoogleScriptStatus.SUCCESSFUL,
  ];
  public readonly status = GoogleScriptStatus;

  @ViewChild('SELECT_ACCOUNT', { read: TemplateRef })
  selectAccountTemplate!: TemplateRef<any>;
  @ViewChild('SELECT_DRIVE', { read: TemplateRef })
  selectDriveTemplate!: TemplateRef<any>;
  @ViewChild('CREATING_SHEET', { read: TemplateRef })
  creatingSheetTemplate!: TemplateRef<any>;
  @ViewChild('ADD_SCRIPT', { read: TemplateRef })
  addScriptTemplate!: TemplateRef<any>;

  private googleAdAccountService = inject(GoogleAdAccountService);
  private destroyRef = inject(DestroyRef);
  private googleDriveService = inject(GoogleDriveService);
  private googleScriptService = inject(GoogleScriptService);
  private toastService = inject(HotToastService);
  private translocoService = inject(TranslocoService);
  private clipboard = inject(Clipboard);

  constructor() {
    effect(() => {
      if (this.activeStep() === Step.CREATING_SHEET) {
        this.submit();
      }
    });
  }

  public ngOnInit(): void {
    this.initForm();
    this.loadAccounts();
  }

  public toggleOpen(): void {
    this.open.update((value) => !value);
  }

  public toggleGuiding(): void {
    if (this.guiding()) {
      this.script.set(null);
      this.firstSubmit.set(false);
      this.percentageDone.set(0);
      this.renderedScript.set(null);

      this.activeStep.set(Step.SELECT_ACCOUNT);
      this.form()?.reset();
    }

    this.guiding.update((value) => !value);
  }

  public loadAccounts(page: number = 1): void {
    if (this.loading()) {
      return;
    }

    this.loading.set(true);

    this.googleAdAccountObservable(page)
      .pipe(
        tap((response) => {
          this.response.set(response);
          this.loading.set(false);
        }),
        catchError((err) => {
          this.loading.set(false);
          return err;
        }),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe();
  }

  public asTemplateRef(step: Step): TemplateRef<any> | null {
    switch (step) {
      case Step.SELECT_ACCOUNT:
        return this.selectAccountTemplate;
      case Step.SELECT_DRIVE:
        return this.selectDriveTemplate;
      case Step.CREATING_SHEET:
        return this.creatingSheetTemplate;
      case Step.ADD_SCRIPT:
        return this.addScriptTemplate;
      default:
        return null;
    }
  }

  public googleAdAccountSourceCallback = (
    page: number,
    filters: { [key: string]: any },
    search: string | null | undefined,
  ): Observable<PaginatedResponse<SelectOption<number>>> => {
    return this.googleAdAccountObservable(page, false, search).pipe(
      map((response) => {
        return {
          ...response,
          data: response.data.map(
            (account) =>
              ({
                value: account.id,
                label:
                  account.name ??
                  this.translocoService.translate('general.unknown'),
                description: account.external_id.toString(),
              }) as SelectOption<number>,
          ),
        } as PaginatedResponse<SelectOption<number>>;
      }),
    );
  };

  public googleDriveSourceCallback = (
    page: number,
    filters: { [key: string]: any },
    search: string | null | undefined,
  ): Observable<PaginatedResponse<SelectOption<number>>> => {
    return this.googleDriveService.index({ page, search }).pipe(
      filter((response): response is PaginatedResponse<GoogleDrive> => true),
      map((response) => ({
        ...response,
        data: response.data.map((drive) => ({
          value: drive.id,
          label: drive.name,
          description: drive.external_id,
        })),
      })),
    );
  };

  public setStep(step: Step): void {
    this.activeStep.set(step);
  }

  public copyScript(): void {
    const script = this.renderedScript();

    if (!script) {
      return;
    }

    this.clipboard.copy(script);

    genericToastSuccess(
      this.toastService,
      this.translocoService,
      'pages.settings.add-ons.quality-score.steps.ADD_SCRIPT.toasts.copy.title',
      'pages.settings.add-ons.quality-score.steps.ADD_SCRIPT.toasts.copy.description',
    );
  }

  public completeGuide(): void {
    if (this.activeStep() !== Step.ADD_SCRIPT) {
      return;
    }

    this.loadAccounts();
    this.toggleGuiding();
  }

  private initForm(): void {
    const form = new FormGroup<Form>({
      google_ad_account_id: new FormControl(null, [Validators.required]),
      google_drive_id: new FormControl(null),
      google_drive_name: new FormControl(null),
    });

    form.controls.google_ad_account_id.valueChanges
      .pipe(
        tap((value) => {
          if (value === null) {
            return;
          }
          this.activeStep.set(Step.SELECT_DRIVE);
        }),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe();

    form.controls.google_drive_id.valueChanges
      .pipe(
        tap((value) => {
          if (value === null) {
            return;
          }
          form.controls.google_drive_name.reset();
          this.activeStep.set(Step.CREATING_SHEET);
        }),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe();

    this.form.set(form);
  }

  private googleAdAccountObservable(
    page: number = 1,
    has_script: boolean = true,
    search: string | null = null,
  ): Observable<PaginatedResponse<GoogleAdAccount>> {
    return this.googleAdAccountService
      .index({
        page,
        script_type: GoogleScriptType.QUALITY_SCORE_CHECKER,
        has_script,
        search,
      })
      .pipe(
        filter(
          (response): response is PaginatedResponse<GoogleAdAccount> => true,
        ),
        takeUntilDestroyed(this.destroyRef),
      );
  }

  private submit(): void {
    const form = this.form();

    if (this.submitting() || !form || form.invalid || !this.firstSubmit()) {
      form?.markAllAsTouched();
      return;
    }

    this.submitting.set(true);
    this.firstSubmit.set(false);

    this.googleScriptService
      .store({
        drive_id: form.controls.google_drive_id.value?.value ?? null,
        drive_name: form.controls.google_drive_name.value,
        ad_account_id: form.controls.google_ad_account_id.value
          ?.value as number,
        type: GoogleScriptType.QUALITY_SCORE_CHECKER,
      })
      .pipe(
        tap((response) => {
          this.script.set(response.data);
          this.submitting.set(false);
          this.setPercentageDone();
          this.pingStatus();
        }),
        catchError((err) => {
          this.submitting.set(false);
          genericToastError(this.toastService, this.translocoService);
          return err;
        }),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe();
  }

  private setPercentageDone(): void {
    const status = this.script()?.status;

    if (!status) {
      return;
    }

    const percentage =
      (this.statusToPercentage.indexOf(status) /
        this.statusToPercentage.length) *
      100;

    this.percentageDone.set(percentage);
  }

  private pingStatus(): void {
    const script = this.script();

    if (!script) {
      return;
    }

    this.googleScriptService
      .show(script)
      .pipe(
        tap((response) => {
          this.script.set(response.data);
          this.setPercentageDone();

          if (
            response.data.status &&
            [
              GoogleScriptStatus.ERROR_CREATING_DRIVE,
              GoogleScriptStatus.ERROR_CREATING_FILE,
              GoogleScriptStatus.SUCCESSFUL,
            ].includes(response.data.status)
          ) {
            if (response.data.status === GoogleScriptStatus.SUCCESSFUL) {
              this.setStep(Step.ADD_SCRIPT);
              this.loadRender();
            }
            return;
          }

          setTimeout(() => {
            this.pingStatus();
          }, 3000);
        }),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe();
  }

  private loadRender(): void {
    const script = this.script();
    if (this.renderLoading() || !script) {
      return;
    }

    this.renderLoading.set(true);

    this.googleScriptService
      .script(script)
      .pipe(
        tap((response) => {
          this.renderedScript.set(response);
          this.renderLoading.set(false);
        }),
        catchError((err) => {
          this.renderLoading.set(false);
          genericToastError(this.toastService, this.translocoService);
          return err;
        }),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe();
  }
}
