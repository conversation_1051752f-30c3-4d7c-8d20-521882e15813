import {
  Component,
  DestroyRef,
  inject,
  OnInit,
  signal,
  TemplateRef,
  ViewChild,
} from '@angular/core';
import {
  TranslocoDirective,
  TranslocoPipe,
  TranslocoService,
} from '@jsverse/transloco';
import { ConnectedSelectComponent } from '@components/form-inputs/connected-select/connected-select.component';
import { NgClass, NgTemplateOutlet } from '@angular/common';
import {
  FormControl,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { PaginatedResponse } from '@api/support/responses/paginated.response';
import { GoogleAdAccount } from '@api/google/models/google-ad-account.interface';
import { catchError, filter, map, Observable, tap } from 'rxjs';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { GoogleScriptType } from '@api/google/enums/google-script-type.enum';
import { GoogleAdAccountService } from '@api/google/services/google-ad-account.service';
import { SelectOption } from '@interfaces/select-option.interface';
import { GoogleDrive } from '@api/google/models/google-drive.interface';
import { GoogleDriveService } from '@api/google/services/google-drive.service';
import { GoogleScriptService } from '@api/google/services/google-script.service';
import { HotToastService } from '@ngxpert/hot-toast';
import {
  genericToastError,
  genericToastSuccess,
} from '@helpers/generic-toasts';

enum Step {
  SELECT_ACCOUNT = 'SELECT_ACCOUNT',
  CREATE_EXPORT = 'CREATE_EXPORT',
  SELECT_DRIVE = 'SELECT_DRIVE',
  FINISHED = 'FINISHED',
}

interface Form {
  ad_account_id: FormControl<SelectOption<number> | null>;
  drive_id: FormControl<SelectOption<number> | null>;
}

@Component({
  selector: 'settings-add-ons-auction-insights',
  imports: [
    TranslocoDirective,
    ConnectedSelectComponent,
    NgTemplateOutlet,
    ReactiveFormsModule,
    NgClass,
    TranslocoPipe,
  ],
  templateUrl: './auction-insights.component.html',
  standalone: true,
})
export class AuctionInsightsComponent implements OnInit {
  public response = signal<PaginatedResponse<GoogleAdAccount> | null>(null);
  public open = signal<boolean>(true);
  public loading = signal<boolean>(false);
  public guiding = signal<boolean>(true);
  public activeStep = signal<Step>(Step.SELECT_ACCOUNT);
  public form = signal<FormGroup<Form> | null>(null);
  public submitting = signal<boolean>(false);

  @ViewChild('SELECT_ACCOUNT', { read: TemplateRef })
  selectAccountTemplate!: TemplateRef<any>;
  @ViewChild('CREATE_EXPORT', { read: TemplateRef })
  createExportTemplate!: TemplateRef<any>;
  @ViewChild('SELECT_DRIVE', { read: TemplateRef })
  selectDriveTemplate!: TemplateRef<any>;
  @ViewChild('FINISHED', { read: TemplateRef })
  finishedTemplate!: TemplateRef<any>;

  public readonly step = Step;
  public readonly steps = Object.values(Step);

  private googleAdAccountService = inject(GoogleAdAccountService);
  private destroyRef = inject(DestroyRef);
  private googleDriveService = inject(GoogleDriveService);
  private googleScriptService = inject(GoogleScriptService);
  private translocoService = inject(TranslocoService);
  private toastService = inject(HotToastService);

  public ngOnInit(): void {
    this.initForm();
    this.loadAccounts();
  }

  public toggleOpen(): void {
    this.open.update((value) => !value);
  }

  public toggleGuiding(): void {
    if (this.guiding()) {
      this.activeStep.set(Step.SELECT_ACCOUNT);
      this.form()?.reset();
    }

    this.guiding.update((value) => !value);
  }

  public loadAccounts(page: number = 1): void {
    if (this.loading()) {
      return;
    }

    this.loading.set(true);

    this.googleAdAccountObservable(page)
      .pipe(
        tap((response) => {
          this.response.set(response);
          this.loading.set(false);
        }),
        catchError((err) => {
          this.loading.set(false);
          return err;
        }),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe();
  }

  public setStep(step: Step): void {
    this.activeStep.set(step);
  }

  public asTemplateRef(step: Step): TemplateRef<any> | null {
    switch (step) {
      case Step.SELECT_ACCOUNT:
        return this.selectAccountTemplate;
      case Step.CREATE_EXPORT:
        return this.createExportTemplate;
      case Step.SELECT_DRIVE:
        return this.selectDriveTemplate;
      case Step.FINISHED:
        return this.finishedTemplate;
      default:
        return null;
    }
  }

  public googleAdAccountSourceCallback = (
    page: number,
    filters: { [key: string]: any },
    search: string | null | undefined,
  ): Observable<PaginatedResponse<SelectOption<number>>> => {
    return this.googleAdAccountObservable(page, false, search).pipe(
      map((response) => {
        return {
          ...response,
          data: response.data.map(
            (account) =>
              ({
                value: account.id,
                label: account.name,
                description: account.external_id.toString(),
              }) as SelectOption<number>,
          ),
        } as PaginatedResponse<SelectOption<number>>;
      }),
    );
  };

  public googleDriveSourceCallback = (
    page: number,
    filters: { [key: string]: any },
    search: string | null | undefined,
  ): Observable<PaginatedResponse<SelectOption<number>>> => {
    return this.googleDriveService.index({ page, search }).pipe(
      filter((response): response is PaginatedResponse<GoogleDrive> => true),
      map((response) => ({
        ...response,
        data: response.data.map((drive) => ({
          value: drive.id,
          label: drive.name,
          description: drive.external_id,
        })),
      })),
    );
  };

  public completeGuide(): void {
    const form = this.form();
    if (this.submitting() || !form || form.invalid) {
      form?.markAllAsTouched();
      return;
    }

    this.submitting.set(true);

    this.googleScriptService
      .store({
        drive_id: form.controls.drive_id.value?.value as number,
        ad_account_id: form.controls.ad_account_id.value?.value as number,
        type: GoogleScriptType.AUCTION_INSIGHTS,
      })
      .pipe(
        tap((response) => {
          this.submitting.set(false);
          this.loadAccounts();
          this.toggleGuiding();
        }),
        catchError((err) => {
          genericToastError(this.toastService, this.translocoService);
          this.submitting.set(false);
          return err;
        }),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe();
  }

  private googleAdAccountObservable(
    page: number = 1,
    has_script: boolean = true,
    search: string | null = null,
  ): Observable<PaginatedResponse<GoogleAdAccount>> {
    return this.googleAdAccountService
      .index({
        page,
        script_type: GoogleScriptType.AUCTION_INSIGHTS,
        has_script,
        search,
      })
      .pipe(
        filter(
          (response): response is PaginatedResponse<GoogleAdAccount> => true,
        ),
        takeUntilDestroyed(this.destroyRef),
      );
  }

  private initForm(): void {
    const form = new FormGroup<Form>({
      ad_account_id: new FormControl(null, [Validators.required]),
      drive_id: new FormControl(null, [Validators.required]),
    });

    form.controls.ad_account_id.valueChanges
      .pipe(
        tap((value) => {
          if (value === null) {
            return;
          }
          this.activeStep.set(Step.CREATE_EXPORT);
        }),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe();

    form.controls.drive_id.valueChanges
      .pipe(
        tap((value) => {
          if (value === null) {
            return;
          }
          this.activeStep.set(Step.FINISHED);
        }),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe();

    this.form.set(form);
  }
}
