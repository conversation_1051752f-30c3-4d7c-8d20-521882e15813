import { GoogleScriptType } from '@api/google/enums/google-script-type.enum';
import { GoogleAdAccount } from '@api/google/models/google-ad-account.interface';
import { GoogleFile } from '@api/google/models/google-file.interface';
import { GoogleScriptStatus } from '@api/google/enums/google-script-status.enum';
import { GoogleScriptError } from '@api/google/enums/google-script-error.enum';

export interface GoogleScript {
  id: number;
  type: GoogleScriptType;
  status: GoogleScriptStatus | null;
  error: GoogleScriptError | null;
  google_ad_account?: GoogleAdAccount;
  google_file?: GoogleFile;
}
